<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-hover: #4f46e5;
            --sidebar-bg: #f9fafb;
            --main-bg: #ffffff;
            --text-primary: #111827;
            --text-secondary: #4b5563;
            --border-color: #e5e7eb;
            --message-user-bg: #f3f4f6;
            --message-ai-bg: #ffffff;
            --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--main-bg);
            color: var(--text-primary);
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        /* Sidebar styles */
        .sidebar {
            width: 260px;
            background-color: var(--sidebar-bg);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 18px;
            color: var(--text-primary);
        }

        .logo img {
            width: 24px;
            height: 24px;
            margin-right: 8px;
        }

        .new-chat-btn {
            width: calc(100% - 32px);
            padding: 10px 16px;
            margin: 16px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }

        .new-chat-btn:hover {
            background-color: var(--primary-hover);
        }

        .new-chat-btn svg {
            margin-right: 8px;
        }

        .chat-history {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
        }

        .chat-item {
            padding: 8px 12px;
            border-radius: 6px;
            margin-bottom: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            color: var(--text-secondary);
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .chat-item:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .chat-item.active {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }

        .chat-item svg {
            margin-right: 8px;
            flex-shrink: 0;
        }

        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid var(--border-color);
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* Main content styles */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
            position: relative;
        }

        .chat-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .model-selector {
            display: flex;
            align-items: center;
        }

        .model-selector select {
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background-color: var(--main-bg);
            font-family: var(--font-family);
            font-size: 14px;
            color: var(--text-primary);
            margin-left: 8px;
        }

        .header-buttons {
            display: flex;
            gap: 8px;
        }

        .ollama-btn {
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background-color: var(--main-bg);
            font-family: var(--font-family);
            font-size: 14px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s;
        }

        .ollama-btn:hover {
            background-color: #f3f4f6;
        }

        .start-ollama-btn {
            background-color: #e8f5e9;
            color: #2e7d32;
            border-color: #c8e6c9;
        }

        .start-ollama-btn:hover {
            background-color: #c8e6c9;
        }

        .check-ollama-btn {
            background-color: #e3f2fd;
            color: #1976d2;
            border-color: #bbdefb;
        }

        .check-ollama-btn:hover {
            background-color: #bbdefb;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            display: flex;
            flex-direction: column;
        }

        .message {
            display: flex;
            margin-bottom: 24px;
            max-width: 80%;
        }

        .message.user-message {
            align-self: flex-end;
        }

        .message.ai-message {
            align-self: flex-start;
        }

        .message.system-message {
            align-self: center;
            max-width: 100%;
            background-color: #fff8e1;
            border-radius: 8px;
            padding: 8px 16px;
            color: #ff8f00;
            font-size: 14px;
        }

        .message.error-message {
            align-self: center;
            max-width: 100%;
            background-color: #ffebee;
            border-radius: 8px;
            padding: 8px 16px;
            color: #d32f2f;
            font-size: 14px;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 12px;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
        }

        .message-avatar.user {
            background-color: #64748b;
        }

        .message-content {
            background-color: var(--message-user-bg);
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 15px;
            line-height: 1.5;
        }

        .ai-message .message-content {
            background-color: var(--message-ai-bg);
            border: 1px solid var(--border-color);
        }

        .input-container {
            padding: 16px;
            border-top: 1px solid var(--border-color);
            position: relative;
        }

        .input-box {
            display: flex;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            overflow: hidden;
            background-color: var(--main-bg);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        #user-input {
            flex: 1;
            padding: 12px 16px;
            border: none;
            font-family: var(--font-family);
            font-size: 15px;
            resize: none;
            min-height: 24px;
            max-height: 200px;
            outline: none;
        }

        .send-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .send-button:hover {
            background-color: var(--primary-hover);
        }

        .send-button:disabled {
            background-color: #d1d5db;
            cursor: not-allowed;
        }

        .status-bar {
            position: absolute;
            bottom: 70px;
            left: 0;
            right: 0;
            text-align: center;
            padding: 12px;
            font-size: 14px;
            color: var(--text-secondary);
            transition: opacity 0.3s;
            border-radius: 8px;
            margin: 0 16px;
            font-weight: 500;
        }

        .status-bar.error {
            color: #d32f2f;
            background-color: #ffebee;
            border: 1px solid #ffcdd2;
        }

        .status-bar.connected {
            color: #2e7d32;
            background-color: #e8f5e9;
            border: 1px solid #c8e6c9;
        }

        .status-bar.loading {
            color: #ed6c02;
            background-color: #fff3e0;
            border: 1px solid #ffe0b2;
        }

        /* Welcome screen */
        .welcome-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 0 32px;
            text-align: center;
        }

        .welcome-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 24px;
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .welcome-subtitle {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 32px;
            max-width: 600px;
        }

        .example-queries {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            width: 100%;
            max-width: 800px;
        }

        .example-query {
            padding: 16px;
            background-color: var(--sidebar-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .example-query:hover {
            background-color: rgba(99, 102, 241, 0.1);
        }

        .example-query h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .example-query p {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .sidebar {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="#6366F1"/>
                    <path d="M2 17L12 22L22 17" stroke="#6366F1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M2 12L12 17L22 12" stroke="#6366F1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>DeepSeek Chat</span>
            </div>
        </div>
        <button class="new-chat-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            New Chat
        </button>
        <div class="chat-history" id="chat-history">
            <!-- Chat history items will be added here -->
        </div>
        <div class="sidebar-footer">
            <div>Powered by Ollama</div>
        </div>
    </div>

    <!-- Main content -->
    <div class="main-content">
        <div class="chat-header">
            <div class="model-selector">
                <label for="model-select">Model:</label>
                <select id="model-select">
                    <option value="llama2">Loading models...</option>
                </select>
            </div>
            <div class="header-buttons">
                <button id="start-ollama" class="ollama-btn start-ollama-btn">
                    Start Ollama
                </button>
                <button id="check-ollama" class="ollama-btn check-ollama-btn">
                    Check Ollama Status
                </button>
            </div>
        </div>

        <!-- Welcome screen (shown initially) -->
        <div class="welcome-screen" id="welcome-screen">
            <svg class="welcome-logo" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="#6366F1"/>
                <path d="M2 17L12 22L22 17" stroke="#6366F1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="#6366F1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <h1 class="welcome-title">Welcome to DeepSeek Chat</h1>
            <p class="welcome-subtitle">
                DeepSeek Chat is powered by Ollama, running locally on your machine.
                Ask anything or try one of the examples below.
            </p>
            <div class="example-queries">
                <div class="example-query" data-query="Explain quantum computing in simple terms">
                    <h3>Explain a concept</h3>
                    <p>Explain quantum computing in simple terms</p>
                </div>
                <div class="example-query" data-query="Write a Python function to find the Fibonacci sequence">
                    <h3>Code generation</h3>
                    <p>Write a Python function to find the Fibonacci sequence</p>
                </div>
                <div class="example-query" data-query="What are some creative ways to reuse plastic bottles?">
                    <h3>Creative ideas</h3>
                    <p>What are some creative ways to reuse plastic bottles?</p>
                </div>
                <div class="example-query" data-query="Write a short story about a robot discovering emotions">
                    <h3>Creative writing</h3>
                    <p>Write a short story about a robot discovering emotions</p>
                </div>
            </div>
        </div>

        <!-- Chat container (hidden initially) -->
        <div class="chat-container" id="chat-container" style="display: none;"></div>

        <div class="status-bar" id="status">Checking Ollama status...</div>

        <div class="input-container">
            <div class="input-box">
                <textarea id="user-input" placeholder="Type your message here..." rows="1" disabled></textarea>
                <button id="send-button" class="send-button" disabled>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22 2L11 13M22 2L15 22L11 13M11 13L2 9L22 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Log initialization for debugging
        console.log('DeepSeek Chat Interface initializing...');

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM content loaded, setting up interface...');
            // DOM elements
            const chatContainer = document.getElementById('chat-container');
            const welcomeScreen = document.getElementById('welcome-screen');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const modelSelect = document.getElementById('model-select');
            const checkOllamaButton = document.getElementById('check-ollama');
            const startOllamaButton = document.getElementById('start-ollama');
            const statusElement = document.getElementById('status');
            const chatHistory = document.getElementById('chat-history');
            const newChatButton = document.querySelector('.new-chat-btn');

            // Track Ollama status
            let ollamaRunning = false;

            // Chat history
            let chats = [];
            let currentChatId = null;

            // Example queries
            document.querySelectorAll('.example-query').forEach(query => {
                query.addEventListener('click', function() {
                    const queryText = this.getAttribute('data-query');
                    userInput.value = queryText;
                    startNewChat();
                    sendMessage();
                });
            });

            // Auto-resize textarea
            userInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });

            // Add system message to chat
            function startNewChat() {
                // Generate a new chat ID
                currentChatId = Date.now().toString();

                // Add to chat history
                const newChat = {
                    id: currentChatId,
                    title: 'New Chat',
                    messages: []
                };

                chats.unshift(newChat);
                updateChatHistory();

                // Clear chat container and show it
                chatContainer.innerHTML = '';
                welcomeScreen.style.display = 'none';
                chatContainer.style.display = 'flex';

                // Add system message
                addMessageToChat('System', 'How can I help you today?', 'system-message');
            }

            // New chat button
            newChatButton.addEventListener('click', function() {
                startNewChat();
            });

            // Check if Ollama is running on page load with multiple attempts
            console.log('Starting initial Ollama connection checks...');

            // Try multiple times with increasing delays
            let initialCheckAttempt = 0;
            const maxInitialAttempts = 5;

            function initialOllamaCheck() {
                console.log(`Initial Ollama check attempt ${initialCheckAttempt + 1}/${maxInitialAttempts}`);
                checkOllama(initialCheckAttempt === 0); // Only show message on first attempt

                // If not connected and we haven't reached max attempts, try again
                if (!ollamaRunning && initialCheckAttempt < maxInitialAttempts - 1) {
                    initialCheckAttempt++;
                    const delay = 1000 * initialCheckAttempt;
                    console.log(`Will retry in ${delay}ms...`);
                    setTimeout(initialOllamaCheck, delay);
                }
            }

            // Start the initial check process
            initialOllamaCheck();

            // Event listeners
            sendButton.addEventListener('click', sendMessage);
            userInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            checkOllamaButton.addEventListener('click', function() {
                checkOllama(true);
            });

            startOllamaButton.addEventListener('click', function() {
                startOllama();
            });

            function startOllama() {
                updateStatus('Attempting to start Ollama...', 'loading');

                // Show message to user
                addMessageToChat('System', 'Attempting to start Ollama...', 'system-message');

                // Try to start Ollama directly using the command line
                try {
                    // Try to run the command directly
                    const startCommand = 'start /b "" ollama serve';

                    // Create a temporary link to download and run the batch file
                    const tempLink = document.createElement('a');
                    tempLink.href = 'start_ollama.bat';
                    tempLink.download = 'start_ollama.bat';
                    tempLink.style.display = 'none';
                    document.body.appendChild(tempLink);

                    // Show instructions to the user
                    addMessageToChat('System', 'Please download and run the Ollama starter file:', 'system-message');

                    // Create a visible download button
                    const downloadDiv = document.createElement('div');
                    downloadDiv.style.display = 'flex';
                    downloadDiv.style.justifyContent = 'center';
                    downloadDiv.style.margin = '15px 0';

                    const downloadButton = document.createElement('a');
                    downloadButton.href = 'start_ollama.bat';
                    downloadButton.download = 'start_ollama.bat';
                    downloadButton.textContent = 'Download Ollama Starter';
                    downloadButton.className = 'ollama-btn start-ollama-btn';
                    downloadButton.style.padding = '10px 20px';
                    downloadButton.style.textDecoration = 'none';
                    downloadButton.style.display = 'inline-block';
                    downloadButton.style.backgroundColor = '#4caf50';
                    downloadButton.style.color = 'white';
                    downloadButton.style.borderRadius = '4px';
                    downloadButton.style.border = 'none';
                    downloadButton.style.cursor = 'pointer';

                    downloadDiv.appendChild(downloadButton);
                    chatContainer.appendChild(downloadDiv);

                    // Add instructions
                    const instructionsDiv = document.createElement('div');
                    instructionsDiv.style.textAlign = 'center';
                    instructionsDiv.style.margin = '10px 0 20px';
                    instructionsDiv.style.fontSize = '14px';
                    instructionsDiv.style.color = '#666';
                    instructionsDiv.innerHTML = 'After downloading, double-click the file to start Ollama.<br>Then click the "Check Ollama Status" button above.';

                    chatContainer.appendChild(instructionsDiv);

                    // Add a check status button
                    const checkDiv = document.createElement('div');
                    checkDiv.style.display = 'flex';
                    checkDiv.style.justifyContent = 'center';
                    checkDiv.style.margin = '5px 0 15px';

                    const checkButton = document.createElement('button');
                    checkButton.textContent = 'Check Ollama Status';
                    checkButton.className = 'ollama-btn check-ollama-btn';
                    checkButton.style.padding = '8px 16px';
                    checkButton.onclick = function() {
                        checkOllama(true);
                    };

                    checkDiv.appendChild(checkButton);
                    chatContainer.appendChild(checkDiv);

                    // Scroll to show the download button
                    chatContainer.scrollTop = chatContainer.scrollHeight;

                    // Check Ollama status after a delay
                    setTimeout(() => {
                        checkOllama(false); // Check silently
                    }, 10000);

                } catch (e) {
                    console.error('Error starting Ollama:', e);
                    showManualInstructions();
                }

                // Function to quickly check if Ollama is running
                function checkOllamaQuickly(callback) {
                    const xhr = new XMLHttpRequest();
                    xhr.timeout = 2000;

                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            if (xhr.status === 200) {
                                callback(true);
                            } else {
                                callback(false);
                            }
                        }
                    };

                    xhr.ontimeout = function() {
                        callback(false);
                    };

                    xhr.onerror = function() {
                        callback(false);
                    };

                    try {
                        xhr.open('GET', 'http://localhost:11434/api/tags', true);
                        xhr.send();
                    } catch (e) {
                        callback(false);
                    }
                }

                // Function to show manual instructions
                function showManualInstructions() {
                    addMessageToChat('System', 'Could not start Ollama automatically. Please use one of these methods:', 'error-message');

                    const instructionsDiv = document.createElement('div');
                    instructionsDiv.className = 'manual-instructions';
                    instructionsDiv.style.padding = '15px';
                    instructionsDiv.style.margin = '10px 0';
                    instructionsDiv.style.backgroundColor = '#f8f9fa';
                    instructionsDiv.style.border = '1px solid #e9ecef';
                    instructionsDiv.style.borderRadius = '8px';

                    instructionsDiv.innerHTML = `
                        <h3 style="margin-top: 0; font-size: 16px;">How to start Ollama:</h3>
                        <div style="margin-bottom: 15px; padding: 10px; background-color: #e8f5e9; border: 1px solid #c8e6c9; border-radius: 6px;">
                            <strong>Method 1: Download and run the Ollama starter</strong>
                            <p style="margin: 5px 0;">Download and run this file to start Ollama:</p>
                            <a href="start_ollama.bat" download style="display: inline-block; padding: 8px 16px; background-color: #4caf50; color: white; text-decoration: none; border-radius: 4px; margin-top: 5px;">Download Ollama Starter</a>
                            <p style="margin: 5px 0; font-size: 12px;">After downloading, double-click the file to run it.</p>
                        </div>

                        <div style="margin-bottom: 15px; padding: 10px; background-color: #e3f2fd; border: 1px solid #bbdefb; border-radius: 6px;">
                            <strong>Method 2: Use the DeepSeek Launcher</strong>
                            <ol style="padding-left: 20px; margin: 5px 0;">
                                <li>Open the DeepSeek Launcher application</li>
                                <li>Click the "Start Ollama" button</li>
                                <li>Wait a few seconds for Ollama to start</li>
                            </ol>
                        </div>

                        <div style="padding: 10px; background-color: #fff3e0; border: 1px solid #ffe0b2; border-radius: 6px;">
                            <strong>Method 3: Command Line</strong>
                            <p style="margin: 5px 0;">Open a command prompt and type:</p>
                            <code style="display: block; padding: 5px; background-color: #f5f5f5; border-radius: 4px; margin: 5px 0;">ollama serve</code>
                        </div>
                    `;

                    chatContainer.appendChild(instructionsDiv);
                    chatContainer.scrollTop = chatContainer.scrollHeight;

                    // Add a retry button
                    const actionDiv = document.createElement('div');
                    actionDiv.style.display = 'flex';
                    actionDiv.style.justifyContent = 'center';
                    actionDiv.style.gap = '10px';
                    actionDiv.style.margin = '10px 0';

                    const retryButton = document.createElement('button');
                    retryButton.textContent = 'Check Again';
                    retryButton.className = 'ollama-btn check-ollama-btn';
                    retryButton.style.padding = '8px 16px';
                    retryButton.onclick = function() {
                        actionDiv.remove();
                        checkOllama(true);
                    };

                    actionDiv.appendChild(retryButton);
                    chatContainer.appendChild(actionDiv);
                }
            }

            function checkOllama(showMessage = false) {
                updateStatus('Checking if Ollama is running...', 'loading');
                sendButton.disabled = true;
                userInput.disabled = true;

                // Make multiple attempts to connect to Ollama
                let attempts = 0;
                const maxAttempts = 5; // Increased attempts

                function attemptConnection() {
                    console.log(`Attempt ${attempts + 1} to connect to Ollama...`);

                    // Use XMLHttpRequest instead of fetch for more control
                    const xhr = new XMLHttpRequest();
                    const timeoutDuration = 3000; // 3 seconds timeout

                    // Set up timeout
                    xhr.timeout = timeoutDuration;

                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            if (xhr.status === 200) {
                                try {
                                    const data = JSON.parse(xhr.responseText);
                                    console.log('Ollama connection successful:', data);
                                    ollamaRunning = true;
                                    updateStatus('Connected to Ollama', 'connected');
                                    sendButton.disabled = false;
                                    userInput.disabled = false;
                                    populateModels(data.models);

                                    if (showMessage) {
                                        addMessageToChat('System', 'Successfully connected to Ollama!', 'system-message');
                                    }
                                } catch (e) {
                                    console.error('Error parsing Ollama response:', e);
                                    handleConnectionError('Invalid response from Ollama');
                                }
                            } else {
                                console.error('Ollama returned status:', xhr.status);
                                handleConnectionError(`Ollama returned status: ${xhr.status}`);
                            }
                        }
                    };

                    xhr.ontimeout = function() {
                        console.error('Ollama connection timeout');
                        handleConnectionError('Connection timeout');
                    };

                    xhr.onerror = function() {
                        console.error('Ollama connection error');
                        handleConnectionError('Connection error');
                    };

                    function handleConnectionError(errorMsg) {
                        attempts++;
                        if (attempts < maxAttempts) {
                            // Try again after a delay with increasing backoff
                            const delay = 1000 * attempts; // Increasing delay for each attempt
                            console.log(`Retrying in ${delay}ms...`);
                            setTimeout(attemptConnection, delay);
                            updateStatus(`Retrying connection to Ollama (attempt ${attempts}/${maxAttempts})...`, 'loading');
                        } else {
                            ollamaRunning = false;
                            updateStatus('Error: Ollama is not running. Please start Ollama using the button above.', 'error');
                            sendButton.disabled = true;
                            userInput.disabled = true;

                            if (showMessage) {
                                addMessageToChat('System', `Error: Could not connect to Ollama. Please click the "Start Ollama" button above.`, 'error-message');

                                // Add download button for the batch file
                                const downloadDiv = document.createElement('div');
                                downloadDiv.style.display = 'flex';
                                downloadDiv.style.justifyContent = 'center';
                                downloadDiv.style.margin = '15px 0';

                                const downloadButton = document.createElement('a');
                                downloadButton.href = 'start_ollama.bat';
                                downloadButton.download = 'start_ollama.bat';
                                downloadButton.textContent = 'Download Ollama Starter';
                                downloadButton.className = 'ollama-btn start-ollama-btn';
                                downloadButton.style.padding = '10px 20px';
                                downloadButton.style.textDecoration = 'none';
                                downloadButton.style.display = 'inline-block';
                                downloadButton.style.backgroundColor = '#4caf50';
                                downloadButton.style.color = 'white';
                                downloadButton.style.borderRadius = '4px';
                                downloadButton.style.border = 'none';
                                downloadButton.style.cursor = 'pointer';

                                downloadDiv.appendChild(downloadButton);
                                chatContainer.appendChild(downloadDiv);

                                // Add instructions
                                const instructionsDiv = document.createElement('div');
                                instructionsDiv.style.textAlign = 'center';
                                instructionsDiv.style.margin = '10px 0 20px';
                                instructionsDiv.style.fontSize = '14px';
                                instructionsDiv.style.color = '#666';
                                instructionsDiv.innerHTML = 'After downloading, double-click the file to start Ollama.<br>Then click the "Check Ollama Status" button above.';

                                chatContainer.appendChild(instructionsDiv);

                                // Add a check status button
                                const checkDiv = document.createElement('div');
                                checkDiv.style.display = 'flex';
                                checkDiv.style.justifyContent = 'center';
                                checkDiv.style.margin = '5px 0 15px';

                                const checkButton = document.createElement('button');
                                checkButton.textContent = 'Check Ollama Status';
                                checkButton.className = 'ollama-btn check-ollama-btn';
                                checkButton.style.padding = '8px 16px';
                                checkButton.onclick = function() {
                                    checkOllama(true);
                                };

                                checkDiv.appendChild(checkButton);
                                chatContainer.appendChild(checkDiv);

                                // Scroll to show the download button
                                chatContainer.scrollTop = chatContainer.scrollHeight;
                            }

                            console.error('Error connecting to Ollama:', errorMsg);
                        }
                    }

                    // Try direct connection first as it's most reliable
                    try {
                        console.log('Trying direct connection to Ollama...');
                        xhr.open('GET', 'http://localhost:11434/api/tags', true);
                        xhr.send();
                    } catch (e) {
                        console.error('Exception sending direct request:', e);

                        // Fall back to proxy server
                        try {
                            console.log('Falling back to proxy server...');
                            const proxyXhr = new XMLHttpRequest();
                            proxyXhr.timeout = timeoutDuration;

                            proxyXhr.onreadystatechange = xhr.onreadystatechange;
                            proxyXhr.ontimeout = xhr.ontimeout;
                            proxyXhr.onerror = xhr.onerror;

                            proxyXhr.open('GET', 'http://localhost:8765/api/tags', true);
                            proxyXhr.send();
                        } catch (e2) {
                            console.error('Exception sending proxy request:', e2);
                            handleConnectionError(e2.message);
                        }
                    }
                }

                // Helper function to add a start Ollama button directly in the chat
                function addStartOllamaButton() {
                    const actionDiv = document.createElement('div');
                    actionDiv.style.display = 'flex';
                    actionDiv.style.justifyContent = 'center';
                    actionDiv.style.gap = '10px';
                    actionDiv.style.margin = '10px 0';

                    const startButton = document.createElement('button');
                    startButton.textContent = 'Start Ollama';
                    startButton.className = 'ollama-btn start-ollama-btn';
                    startButton.style.padding = '8px 16px';
                    startButton.onclick = function() {
                        actionDiv.remove();
                        startOllama();
                    };

                    actionDiv.appendChild(startButton);
                    chatContainer.appendChild(actionDiv);
                }

                attemptConnection();
            }

            function populateModels(models) {
                console.log('Populating models:', models);

                // Clear existing options
                modelSelect.innerHTML = '';

                if (!models || models.length === 0) {
                    console.log('No models found, adding default options');
                    // Add default options if no models found
                    modelSelect.innerHTML = `
                        <option value="llama2">Llama 2</option>
                        <option value="mistral">Mistral</option>
                        <option value="codellama">Code Llama</option>
                    `;
                    updateStatus('No models found. Please download a model first.', 'error');
                    if (currentChatId) {
                        addMessageToChat('System', 'No models found. Please download a model using the launcher.', 'error-message');
                    }
                    return;
                }

                // Add found models
                console.log(`Adding ${models.length} models to select dropdown`);
                models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.textContent = model.name;
                    modelSelect.appendChild(option);
                    console.log(`Added model: ${model.name}`);
                });

                updateStatus(`Found ${models.length} models. Ready to chat!`, 'connected');

                // If we have a welcome screen, add a message about the models
                if (welcomeScreen.style.display !== 'none') {
                    const modelsList = models.map(m => m.name).join(', ');
                    addMessageToChat('System', `Connected to Ollama! Available models: ${modelsList}`, 'system-message');
                }
            }

            function updateStatus(message, type = '') {
                statusElement.textContent = message;
                statusElement.className = 'status-bar';
                if (type) {
                    statusElement.classList.add(type);
                }
            }

            function sendMessage() {
                const message = userInput.value.trim();
                if (!message) return;

                // Start a new chat if none exists
                if (!currentChatId) {
                    startNewChat();
                }

                // Add user message to chat
                addMessageToChat('You', message, 'user-message');

                // Update chat title with first few words of first message
                if (chats.length > 0 && chats[0].messages.length === 0) {
                    const title = message.split(' ').slice(0, 4).join(' ') + '...';
                    chats[0].title = title;
                    updateChatHistory();
                }

                // Store message in chat history
                if (currentChatId && chats.length > 0) {
                    const currentChat = chats.find(chat => chat.id === currentChatId);
                    if (currentChat) {
                        currentChat.messages.push({
                            role: 'user',
                            content: message
                        });
                    }
                }

                // Clear input and reset height
                userInput.value = '';
                userInput.style.height = 'auto';

                // Disable send button and update status
                sendButton.disabled = true;
                userInput.disabled = true;
                updateStatus('Waiting for response...', 'loading');

                // Get selected model
                const selectedModel = modelSelect.value;

                // Try direct connection to Ollama API first
                console.log(`Sending message to Ollama using model: ${selectedModel}`);
                fetch('http://localhost:11434/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: selectedModel,
                        prompt: message,
                        stream: false
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`API request failed with status ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Add AI response to chat
                    addMessageToChat('AI', data.response, 'ai-message');
                    updateStatus('Ready', 'connected');

                    // Store response in chat history
                    if (currentChatId && chats.length > 0) {
                        const currentChat = chats.find(chat => chat.id === currentChatId);
                        if (currentChat) {
                            currentChat.messages.push({
                                role: 'assistant',
                                content: data.response
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('Error with direct connection:', error);

                    // Try via proxy as fallback
                    console.log('Trying via proxy server as fallback...');
                    fetch('http://localhost:8765/api/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            model: selectedModel,
                            prompt: message,
                            stream: false
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`API request via proxy failed with status ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Add AI response to chat
                        addMessageToChat('AI', data.response, 'ai-message');
                        updateStatus('Ready (via proxy)', 'connected');

                        // Store response in chat history
                        if (currentChatId && chats.length > 0) {
                            const currentChat = chats.find(chat => chat.id === currentChatId);
                            if (currentChat) {
                                currentChat.messages.push({
                                    role: 'assistant',
                                    content: data.response
                                });
                            }
                        }
                    })
                    .catch(proxyError => {
                        console.error('Error with proxy connection:', proxyError);
                        updateStatus(`Error: ${error.message}. Make sure Ollama is running and the model is downloaded.`, 'error');
                        addMessageToChat('System', `Error: Could not get a response. Please make sure Ollama is running by clicking the "Start Ollama" button above.`, 'error-message');

                        // Try to reconnect to Ollama
                        setTimeout(checkOllama, 1000);

                        // Suggest starting Ollama
                        if (!ollamaRunning) {
                        addMessageToChat('System', 'Would you like to start Ollama now?', 'system-message');

                        // Add action buttons
                        const actionDiv = document.createElement('div');
                        actionDiv.style.display = 'flex';
                        actionDiv.style.justifyContent = 'center';
                        actionDiv.style.gap = '10px';
                        actionDiv.style.margin = '10px 0';

                        const startButton = document.createElement('button');
                        startButton.textContent = 'Start Ollama';
                        startButton.className = 'ollama-btn start-ollama-btn';
                        startButton.style.padding = '8px 16px';
                        startButton.onclick = function() {
                            actionDiv.remove();
                            startOllama();
                        };

                        actionDiv.appendChild(startButton);
                        chatContainer.appendChild(actionDiv);
                    }
                })
                .finally(() => {
                    // Re-enable send button if Ollama is connected
                    if (statusElement.classList.contains('connected')) {
                        sendButton.disabled = false;
                        userInput.disabled = false;
                        userInput.focus();
                    }
                });
            }

            function addMessageToChat(sender, message, className) {
                // If welcome screen is visible, hide it and show chat container
                if (welcomeScreen.style.display !== 'none') {
                    welcomeScreen.style.display = 'none';
                    chatContainer.style.display = 'flex';
                }

                if (className === 'system-message' || className === 'error-message') {
                    const messageElement = document.createElement('div');
                    messageElement.classList.add('message', className);
                    messageElement.textContent = message;
                    chatContainer.appendChild(messageElement);
                } else {
                    const messageElement = document.createElement('div');
                    messageElement.classList.add('message', className);

                    const avatarElement = document.createElement('div');
                    avatarElement.classList.add('message-avatar');
                    if (sender === 'You') {
                        avatarElement.classList.add('user');
                        avatarElement.textContent = 'U';
                    } else {
                        avatarElement.textContent = 'A';
                    }

                    const contentElement = document.createElement('div');
                    contentElement.classList.add('message-content');
                    contentElement.innerHTML = formatMessage(message);

                    messageElement.appendChild(avatarElement);
                    messageElement.appendChild(contentElement);
                    chatContainer.appendChild(messageElement);
                }

                // Scroll to bottom
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            function formatMessage(message) {
                // Convert URLs to links
                const urlRegex = /(https?:\/\/[^\s]+)/g;
                let formattedMessage = message.replace(urlRegex, url => `<a href="${url}" target="_blank">${url}</a>`);

                // Convert newlines to <br>
                formattedMessage = formattedMessage.replace(/\n/g, '<br>');

                // Format code blocks
                formattedMessage = formattedMessage.replace(/```(\w+)?\n([\s\S]*?)\n```/g, function(match, language, code) {
                    return `<pre><code class="${language || ''}">${code}</code></pre>`;
                });

                return formattedMessage;
            }

            function updateChatHistory() {
                chatHistory.innerHTML = '';

                chats.forEach(chat => {
                    const chatItem = document.createElement('div');
                    chatItem.classList.add('chat-item');
                    if (chat.id === currentChatId) {
                        chatItem.classList.add('active');
                    }

                    chatItem.innerHTML = `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        ${chat.title}
                    `;

                    chatItem.addEventListener('click', function() {
                        loadChat(chat.id);
                    });

                    chatHistory.appendChild(chatItem);
                });
            }

            function loadChat(chatId) {
                const chat = chats.find(c => c.id === chatId);
                if (!chat) return;

                currentChatId = chatId;
                chatContainer.innerHTML = '';
                welcomeScreen.style.display = 'none';
                chatContainer.style.display = 'flex';

                // Add system message
                addMessageToChat('System', 'Chat history loaded', 'system-message');

                // Add all messages
                chat.messages.forEach(msg => {
                    if (msg.role === 'user') {
                        addMessageToChat('You', msg.content, 'user-message');
                    } else if (msg.role === 'assistant') {
                        addMessageToChat('AI', msg.content, 'ai-message');
                    }
                });

                updateChatHistory();
            }
        });
    </script>
</body>
</html>
